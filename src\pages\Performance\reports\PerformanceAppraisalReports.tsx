import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Screen } from '@/app-components/layout/screen';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import {
  Search,
  Filter,
  Download,
  FileText,
  Users,
  UsersRound,
  Building2,
  UserCheck,
  AlertCircle,
  RefreshCw,
  X,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import {
  Breadcrumb,
  <PERSON>readcrumbI<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ink,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>crumbP<PERSON>,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import * as XLSX from 'xlsx';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

// Types
interface AppraisalRecord {
  id: number;
  employeeid_id: string;
  supervisor_id: string;
  status: string;
  final_supervisor_comments: string | null;
  total_supervisor_rating_score: number | null;
  final_hr_comments: string | null;
  total_emp_self_rating_score: number | null;
  first_name: string;
  last_name: string;
  supervisor_first_name: string;
  supervisor_last_name: string;
  employee_comments: string;
  supervisor_comments: string;
  supervisor_rating: number;
  employee_rating: number;
  what_id: number;
  MIB_target: number | null;
  MIB_achieved: number | null;
  Sales_target: number | null;
  Sales_achieved: number | null;
  what: string;
  how: string;
}

interface GroupedKPI {
  what_id: number;
  what: string;
  employee_rating: number;
  supervisor_rating: number;
  employee_comments: string;
  supervisor_comments: string;
  how_items: {
    how: string;
    MIB_target: number | null;
    MIB_achieved: number | null;
    Sales_target: number | null;
    Sales_achieved: number | null;
  }[];
}

interface GroupedAppraisalRecord {
  employeeid_id: string;
  first_name: string;
  last_name: string;
  supervisor_id: string;
  supervisor_first_name: string;
  supervisor_last_name: string;
  status: string;
  total_kpis: number;
  total_supervisor_rating_score: number | null;
  total_emp_self_rating_score: number | null;
  final_supervisor_comments: string | null;
  final_hr_comments: string | null;
  grouped_kpis: GroupedKPI[];
  expanded?: boolean;
}

interface Department {
  id: number;
  name: string;
  title?: string;
}

interface Team {
  id: number;
  name: string;
  description: string;
  group_head: string;
  group_assistant: string;
  group_hr: string;
  group_status_active: boolean;
  organisation: number;
  parent_group: number | null;
}

interface AppraisalFilters {
  employee_no?: string;
  department?: string;
  team?: string;
  status?: string;
  search?: string;
}

// Custom Expandable Table Component
interface ExpandableAppraisalTableProps {
  data: GroupedAppraisalRecord[];
  currentPage: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

const ExpandableAppraisalTable: React.FC<ExpandableAppraisalTableProps> = ({
  data,
  currentPage,
  itemsPerPage,
  onPageChange
}) => {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleRow = (employeeId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(employeeId)) {
      newExpanded.delete(employeeId);
    } else {
      newExpanded.add(employeeId);
    }
    setExpandedRows(newExpanded);
  };

  // Calculate pagination
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = data.slice(startIndex, endIndex);

  return (
    <div className="space-y-4">
      {/* Data */}
      <div className="space-y-2">
        {paginatedData.map((employee) => (
        <Card key={employee.employeeid_id} className="overflow-hidden">
          <Collapsible
            open={expandedRows.has(employee.employeeid_id)}
            onOpenChange={() => toggleRow(employee.employeeid_id)}
          >
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {expandedRows.has(employee.employeeid_id) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                    <div>
                      <CardTitle className="text-lg">
                        {employee.first_name} {employee.last_name}
                      </CardTitle>
                      <CardDescription>
                        {employee.employeeid_id} • Supervisor: {employee.supervisor_first_name} {employee.supervisor_last_name}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    {getStatusBadge(employee.status)}
                    <div className="text-right">
                      <div className="text-sm font-medium">{employee.total_kpis} KPIs</div>
                      <div className="text-xs text-muted-foreground">
                        Self: {employee.total_emp_self_rating_score || 'N/A'} |
                        Supervisor: {employee.total_supervisor_rating_score || 'N/A'}
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Summary Comments */}
                  {(employee.final_supervisor_comments || employee.final_hr_comments) && (
                    <div className="grid gap-4 md:grid-cols-2">
                      {employee.final_supervisor_comments && (
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Final Supervisor Comments</h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
                            {employee.final_supervisor_comments}
                          </p>
                        </div>
                      )}
                      {employee.final_hr_comments && (
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Final HR Comments</h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
                            {employee.final_hr_comments}
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Grouped KPI Records */}
                  <div className="space-y-4">
                    <h4 className="font-medium">KPI Performance Details</h4>
                    <div className="grid gap-4">
                      {employee.grouped_kpis.map((kpi, kpiIndex) => (
                        <Card key={`${kpi.what_id}-${kpiIndex}`} className="border-l-4 border-l-blue-500">
                          <CardContent className="pt-4">
                            <div className="space-y-4">
                              {/* KPI Header */}
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <h5 className="font-medium text-base mb-2">{kpi.what}</h5>
                                  <div className="flex gap-4 text-sm">
                                    <span>Employee Rating: <Badge variant="outline" className="bg-blue-50 text-blue-700">{kpi.employee_rating}</Badge></span>
                                    <span>Supervisor Rating: <Badge variant="outline" className="bg-green-50 text-green-700">{kpi.supervisor_rating}</Badge></span>
                                  </div>
                                </div>
                              </div>

                              {/* KPI Comments */}
                              {(kpi.employee_comments || kpi.supervisor_comments) && (
                                <div className="grid gap-4 md:grid-cols-2 mb-4">
                                  {kpi.employee_comments && (
                                    <div className="space-y-2">
                                      <h6 className="font-medium text-sm">Employee Comments</h6>
                                      <p className="text-sm text-muted-foreground bg-blue-50 p-3 rounded">
                                        {kpi.employee_comments}
                                      </p>
                                    </div>
                                  )}
                                  {kpi.supervisor_comments && (
                                    <div className="space-y-2">
                                      <h6 className="font-medium text-sm">Supervisor Comments</h6>
                                      <p className="text-sm text-muted-foreground bg-green-50 p-3 rounded">
                                        {kpi.supervisor_comments}
                                      </p>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* How Items */}
                              <div className="space-y-3">
                                <h6 className="font-medium text-sm text-muted-foreground">Performance Areas ({kpi.how_items.length} items)</h6>
                                {kpi.how_items.map((howItem, howIndex) => (
                                  <div key={howIndex} className="bg-muted/30 p-3 rounded-lg">
                                    <div className="grid gap-3 md:grid-cols-2">
                                      <div>
                                        <p className="text-sm font-medium">{howItem.how}</p>
                                      </div>
                                      <div className="space-y-2">
                                        {(howItem.MIB_target || howItem.Sales_target) && (
                                          <div className="grid grid-cols-2 gap-2 text-xs">
                                            {howItem.MIB_target && (
                                              <div>
                                                <span className="font-medium">MIB:</span> {howItem.MIB_achieved || 0}/{howItem.MIB_target}
                                              </div>
                                            )}
                                            {howItem.Sales_target && (
                                              <div>
                                                <span className="font-medium">Sales:</span> {howItem.Sales_achieved || 0}/{howItem.Sales_target}
                                              </div>
                                            )}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {startIndex + 1} to {Math.min(endIndex, data.length)} of {data.length} employees
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(page)}
                  className="w-8 h-8 p-0"
                >
                  {page}
                </Button>
              ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

const PerformanceAppraisalReports: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State
  const [data, setData] = useState<GroupedAppraisalRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [filters, setFilters] = useState<AppraisalFilters>({});
  const [isExporting, setIsExporting] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  // Debounced filters for API calls
  const [debouncedFilters, setDebouncedFilters] = useState<AppraisalFilters>({});

  // Simple debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters]);

  // Fetch departments and teams
  useEffect(() => {
    const fetchMetadata = async () => {
      if (!token) return;

      try {
        // Fetch departments
        const deptRes = await fetch(`${BASE_URL}/users/departments`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (deptRes.ok) {
          const deptData = await deptRes.json();
          setDepartments(deptData);
        }

        // Fetch teams (organization groups)
        const teamsRes = await fetch(`${BASE_URL}/users/organization_groups`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (teamsRes.ok) {
          const teamsData = await teamsRes.json();
          setTeams(teamsData);
        }
      } catch (err) {
        console.error('Error fetching metadata:', err);
      }
    };

    fetchMetadata();
  }, [token]);

  // Fetch appraisal data
  const fetchAppraisalData = useCallback(async (filterParams: AppraisalFilters) => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.employee_no) params.append('employee_no', filterParams.employee_no);
      if (filterParams.department && filterParams.department !== 'all') params.append('department', filterParams.department);
      if (filterParams.team && filterParams.team !== 'all') params.append('team', filterParams.team);

      const url = `${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      // Group records by employee ID
      const groupedData = groupRecordsByEmployee(result.results || []);
      
      // Apply client-side filters
      const filteredData = applyClientSideFilters(groupedData, filterParams);
      
      setData(filteredData);
      setTotalResults(result['Total Results'] || result.count || filteredData.length);
      
    } catch (err: any) {
      console.error('Error fetching appraisal data:', err);
      setError(err.message || 'Failed to fetch appraisal data');
      toast({
        title: "Error",
        description: "Failed to fetch appraisal data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [token, toast]);

  // Group records by employee ID and then by what_id
  const groupRecordsByEmployee = (records: AppraisalRecord[]): GroupedAppraisalRecord[] => {
    const grouped = records.reduce((acc, record) => {
      const key = record.employeeid_id;

      if (!acc[key]) {
        acc[key] = {
          employeeid_id: record.employeeid_id,
          first_name: record.first_name,
          last_name: record.last_name,
          supervisor_id: record.supervisor_id,
          supervisor_first_name: record.supervisor_first_name,
          supervisor_last_name: record.supervisor_last_name,
          status: record.status,
          total_kpis: 0,
          total_supervisor_rating_score: record.total_supervisor_rating_score,
          total_emp_self_rating_score: record.total_emp_self_rating_score,
          final_supervisor_comments: record.final_supervisor_comments,
          final_hr_comments: record.final_hr_comments,
          grouped_kpis: [],
        };
      }

      // Find existing KPI group or create new one
      let kpiGroup = acc[key].grouped_kpis.find(kpi => kpi.what_id === record.what_id);

      if (!kpiGroup) {
        kpiGroup = {
          what_id: record.what_id,
          what: record.what,
          employee_rating: record.employee_rating,
          supervisor_rating: record.supervisor_rating,
          employee_comments: record.employee_comments,
          supervisor_comments: record.supervisor_comments,
          how_items: [],
        };
        acc[key].grouped_kpis.push(kpiGroup);
      }

      // Add the "how" item to this KPI group (only if not already added)
      const existingHow = kpiGroup.how_items.find(item => item.how === record.how);
      if (!existingHow) {
        kpiGroup.how_items.push({
          how: record.how,
          MIB_target: record.MIB_target,
          MIB_achieved: record.MIB_achieved,
          Sales_target: record.Sales_target,
          Sales_achieved: record.Sales_achieved,
        });
      }

      return acc;
    }, {} as Record<string, GroupedAppraisalRecord>);

    // Update total KPI count for each employee
    Object.values(grouped).forEach(employee => {
      employee.total_kpis = employee.grouped_kpis.length;
    });

    return Object.values(grouped);
  };

  // Apply client-side filters
  const applyClientSideFilters = (data: GroupedAppraisalRecord[], filterParams: AppraisalFilters): GroupedAppraisalRecord[] => {
    return data.filter(record => {
      // Status filter
      if (filterParams.status && filterParams.status !== 'all' && record.status !== filterParams.status) {
        return false;
      }

      // Search filter (searches in name, employee ID, comments, KPIs)
      if (filterParams.search) {
        const searchTerm = filterParams.search.toLowerCase();
        const searchableText = [
          record.first_name,
          record.last_name,
          record.employeeid_id,
          record.final_supervisor_comments,
          record.final_hr_comments,
          ...record.grouped_kpis.map(kpi => kpi.what),
          ...record.grouped_kpis.flatMap(kpi => kpi.how_items.map(item => item.how)),
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });
  };

  // Fetch data when debounced filters change
  useEffect(() => {
    fetchAppraisalData(debouncedFilters);
  }, [debouncedFilters, fetchAppraisalData]);

  // Filter handlers
  const handleFilterChange = (key: keyof AppraisalFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value || undefined
    }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const clearFilters = () => {
    setFilters({});
  };

  const refreshData = () => {
    fetchAppraisalData(debouncedFilters);
  };

  // Export functionality
  const handleExport = async (format: 'excel' | 'pdf') => {
    if (data.length === 0) {
      toast({
        title: "No Data",
        description: "No data available to export.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);

    try {
      if (format === 'excel') {
        await exportToExcel();
      } else {
        await exportToPDF();
      }

      toast({
        title: "Export Successful",
        description: `Data exported to ${format.toUpperCase()} successfully.`,
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const exportToExcel = async () => {
    // Flatten data for Excel export
    const exportData = data.flatMap(employee =>
      employee.grouped_kpis.flatMap(kpi =>
        kpi.how_items.map(howItem => ({
          'Employee ID': employee.employeeid_id,
          'Employee Name': `${employee.first_name} ${employee.last_name}`,
          'Supervisor': `${employee.supervisor_first_name} ${employee.supervisor_last_name}`,
          'Supervisor ID': employee.supervisor_id,
          'Status': employee.status,
          'KPI': kpi.what,
          'How': howItem.how,
          'KPI Employee Rating': kpi.employee_rating,
          'KPI Supervisor Rating': kpi.supervisor_rating,
          'KPI Employee Comments': kpi.employee_comments,
          'KPI Supervisor Comments': kpi.supervisor_comments,
          'MIB Target': howItem.MIB_target || 'N/A',
          'MIB Achieved': howItem.MIB_achieved || 'N/A',
          'Sales Target': howItem.Sales_target || 'N/A',
          'Sales Achieved': howItem.Sales_achieved || 'N/A',
          'Total Self Rating': employee.total_emp_self_rating_score || 'N/A',
          'Total Supervisor Rating': employee.total_supervisor_rating_score || 'N/A',
          'Final Supervisor Comments': employee.final_supervisor_comments || 'N/A',
          'Final HR Comments': employee.final_hr_comments || 'N/A',
        }))
      )
    );

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Appraisal Reports');

    // Auto-size columns
    const colWidths = Object.keys(exportData[0] || {}).map(key => ({
      wch: Math.max(key.length, 15)
    }));
    worksheet['!cols'] = colWidths;

    const fileName = `Performance_Appraisal_Reports_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);
  };

  const exportToPDF = async () => {
    const { default: pdfMake } = await import('pdfmake/build/pdfmake');
    const { default: pdfFonts } = await import('pdfmake/build/vfs_fonts');

    pdfMake.vfs = pdfFonts.vfs;

    // Create table data
    const tableBody = [
      ['Employee ID', 'Name', 'Supervisor', 'Status', 'KPI Count', 'Self Rating', 'Supervisor Rating']
    ];

    data.forEach(employee => {
      tableBody.push([
        employee.employeeid_id,
        `${employee.first_name} ${employee.last_name}`,
        `${employee.supervisor_first_name} ${employee.supervisor_last_name}`,
        employee.status,
        employee.total_kpis.toString(),
        employee.total_emp_self_rating_score?.toString() || 'N/A',
        employee.total_supervisor_rating_score?.toString() || 'N/A'
      ]);
    });

    const docDefinition = {
      content: [
        {
          text: 'Performance Appraisal Reports',
          style: 'header',
          alignment: 'center' as const,
          margin: [0, 0, 0, 20] as [number, number, number, number]
        },
        {
          text: `Generated on: ${new Date().toLocaleDateString()}`,
          style: 'subheader',
          alignment: 'center' as const,
          margin: [0, 0, 0, 20] as [number, number, number, number]
        },
        {
          text: `Total Employees: ${data.length}`,
          margin: [0, 0, 0, 10] as [number, number, number, number]
        },
        {
          table: {
            headerRows: 1,
            widths: ['auto', '*', 'auto', 'auto', 'auto', 'auto', 'auto'],
            body: tableBody
          },
          layout: 'lightHorizontalLines'
        }
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true
        },
        subheader: {
          fontSize: 12,
          italics: true
        }
      },
      pageOrientation: 'landscape' as const,
      pageMargins: [40, 60, 40, 60] as [number, number, number, number]
    };

    const fileName = `Performance_Appraisal_Reports_${new Date().toISOString().split('T')[0]}.pdf`;
    pdfMake.createPdf(docDefinition).download(fileName);
  };

  return (
    <Screen>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/performance-dashboard">Performance</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbPage>Appraisal Reports</BreadcrumbPage>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Performance Appraisal Reports</h1>
            <p className="text-muted-foreground">
              Comprehensive performance appraisal reports with advanced filtering and export capabilities
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshData}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{loading ? <Skeleton className="h-8 w-16" /> : data.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Records</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? <Skeleton className="h-8 w-16" /> : data.reduce((sum, emp) => sum + emp.total_kpis, 0)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Departments</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{departments.length}</div>
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <UsersRound className="h-3 w-3" />
                {teams.length} Teams
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Reviews</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? <Skeleton className="h-8 w-16" /> : data.filter(emp => emp.status === 'Completed').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
            <CardDescription>
              Filter appraisal reports by various criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search employees, comments..."
                    value={filters.search || ''}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Employee Number */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Employee Number</label>
                <Input
                  placeholder="Enter employee number"
                  value={filters.employee_no || ''}
                  onChange={(e) => handleFilterChange('employee_no', e.target.value)}
                />
              </div>

              {/* Department */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Department</label>
                <Select
                  value={filters.department || 'all'}
                  onValueChange={(value) => handleFilterChange('department', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id.toString()}>
                        {dept.name || dept.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Team */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Team</label>
                <Select
                  value={filters.team || 'all'}
                  onValueChange={(value) => handleFilterChange('team', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select team" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Teams</SelectItem>
                    {teams.filter(team => team.group_status_active).map((team) => (
                      <SelectItem key={team.id} value={team.id.toString()}>
                        {team.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>



              {/* Status */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select
                  value={filters.status || 'all'}
                  onValueChange={(value) => handleFilterChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="Open">Open Appraisals</SelectItem>
                    <SelectItem value="Self Appraised">Self Appraised</SelectItem>
                    <SelectItem value="Employee Accepted">Employee Accepted</SelectItem>
                    <SelectItem value="Supervisor Accepted">Supervisor Accepted</SelectItem>
                    <SelectItem value="In Review">In Supervisor Review</SelectItem>
                    <SelectItem value="Supervisor Appraised">Supervisor Appraised</SelectItem>
                    <SelectItem value="HR Appraised">HR Appraised</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Clear Filters */}
              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="w-full"
                  disabled={Object.keys(filters).length === 0}
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>

            {/* Active Filters Display */}
            {Object.keys(filters).length > 0 && (
              <div className="mt-4 flex flex-wrap gap-2">
                <span className="text-sm font-medium">Active Filters:</span>
                {Object.entries(filters).map(([key, value]) => {
                  if (!value) return null;

                  // Get display value for filters
                  let displayValue = value;
                  if (key === 'department' && value !== 'all') {
                    const dept = departments.find(d => d.id.toString() === value);
                    displayValue = dept ? (dept.name || dept.title) : value;
                  } else if (key === 'team' && value !== 'all') {
                    const team = teams.find(t => t.id.toString() === value);
                    displayValue = team ? team.name : value;
                  }

                  return (
                    <Badge key={key} variant="secondary" className="flex items-center gap-1">
                      {key}: {displayValue}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => handleFilterChange(key as keyof AppraisalFilters, '')}
                      />
                    </Badge>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-5 w-5" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Data Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Appraisal Reports ({totalResults} employees)</span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('excel')}
                  disabled={isExporting || data.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {isExporting ? 'Exporting...' : 'Export Excel'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('pdf')}
                  disabled={isExporting || data.length === 0}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  {isExporting ? 'Exporting...' : 'Export PDF'}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            ) : (
              <ExpandableAppraisalTable
                data={data}
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                onPageChange={setCurrentPage}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
};

// Column definitions
const getStatusBadge = (status: string) => {
  const statusConfig = {
    'Open': { variant: 'outline' as const, color: 'bg-gray-100 text-gray-800' },
    'Self Appraised': { variant: 'secondary' as const, color: 'bg-sky-100 text-sky-800' },
    'Employee Accepted': { variant: 'default' as const, color: 'bg-emerald-100 text-emerald-800' },
    'Supervisor Accepted': { variant: 'default' as const, color: 'bg-teal-100 text-teal-800' },
    'In Review': { variant: 'secondary' as const, color: 'bg-orange-100 text-orange-800' },
    'Supervisor Appraised': { variant: 'secondary' as const, color: 'bg-amber-100 text-amber-800' },
    'HR Appraised': { variant: 'default' as const, color: 'bg-indigo-100 text-indigo-800' },
    'Completed': { variant: 'default' as const, color: 'bg-green-100 text-green-800' },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'outline' as const, color: 'bg-gray-100 text-gray-800' };

  return (
    <Badge variant={config.variant} className={config.color}>
      {status}
    </Badge>
  );
};

export default PerformanceAppraisalReports;
