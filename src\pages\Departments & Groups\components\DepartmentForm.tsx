import React from "react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import * as z from "zod";

// Types
interface Employee {
  employee_no: string;
  username: string;
  first_name: string;
  last_name: string;
}

interface Department {
  id: number;
  name: string;
  description: string;
  dep_head: string;
  dep_head_assistant: string;
  dep_hr: string;
  department_status_active: boolean;
  organisation: number;
  parent_department: number | null;
}

const departmentSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().min(1, "Description is required"),
  dep_head: z.string().min(1, "Department head is required").max(60, "Department head must be less than 60 characters"),
  dep_head_assistant: z.string().min(1, "Department head assistant is required").max(60, "Department head assistant must be less than 60 characters"),
  dep_hr: z.string().min(1, "Department HR is required").max(60, "Department HR must be less than 60 characters"),
  department_status_active: z.boolean().default(true),
  organisation: z.number(),
  parent_department: z.number({ required_error: "Parent department is required" })
    .min(1, "Parent department is required"),
});

interface DepartmentFormProps {
  form: UseFormReturn<z.infer<typeof departmentSchema>>;
  onSubmit: (data: z.infer<typeof departmentSchema>) => void;
  isSubmitting: boolean;
  employees: Employee[];
  departments: Department[];
  submitButtonText: string;
  formError?: string;
}

// EmployeeSelect Component
const EmployeeSelect: React.FC<{
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  employees: Employee[];
}> = ({ value, onChange, placeholder = "Select an employee", employees }) => {
  const [search, setSearch] = React.useState("");

  // Helper function to get full name
  const getFullName = (emp: Employee) => {
    const fullName = `${emp.first_name} ${emp.last_name}`.trim();
    return fullName || emp.username; // Fallback to username if names are empty
  };

  const filtered = employees.filter((emp) => {
    const fullName = getFullName(emp);
    return fullName.toLowerCase().includes(search.toLowerCase()) ||
           emp.username.toLowerCase().includes(search.toLowerCase());
  });

  // Get the display name for the selected value
  const selectedEmployee = employees.find(emp => emp.employee_no === value);
  const selectedDisplayName = selectedEmployee ? getFullName(selectedEmployee) : placeholder;

  return (
    <Select onValueChange={onChange} value={value}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder}>
          {value && selectedEmployee ? getFullName(selectedEmployee) : placeholder}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <div className="p-2">
          <Input
            placeholder="Search..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        {filtered.map((emp) => (
          <SelectItem key={emp.employee_no} value={emp.employee_no}>
            {getFullName(emp)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

// DepartmentSelect Component
const DepartmentSelect: React.FC<{
  value: number | null;
  onChange: (value: number) => void;
  placeholder?: string;
  departments: Department[];
}> = ({ value, onChange, placeholder = "Select a department", departments }) => {
  const [search, setSearch] = React.useState("");
  const options = departments.map((dept) => ({
    value: dept.id,
    label: dept.name,
  }));
  const filteredOptions = options.filter((opt) =>
    opt.label.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <Select
      onValueChange={(val) => onChange(parseInt(val))}
      value={value ? value.toString() : ""}
    >
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <div className="p-2">
          <Input
            placeholder="Search..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        {filteredOptions.map((opt) => (
          <SelectItem key={opt.value} value={opt.value.toString()}>
            {opt.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default function DepartmentForm({
  form,
  onSubmit,
  isSubmitting,
  employees,
  departments,
  submitButtonText,
  formError,
}: DepartmentFormProps) {
  return (
    <>
      {formError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 mb-4">
          <p className="text-red-600 dark:text-red-400 text-sm">{formError}</p>
        </div>
      )}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Department Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter department name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea placeholder="Enter department description" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="dep_head"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department Head</FormLabel>
                  <FormControl>
                    <EmployeeSelect
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Select Department Head"
                      employees={employees}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="dep_head_assistant"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Assistant Head</FormLabel>
                  <FormControl>
                    <EmployeeSelect
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Select Assistant Head"
                      employees={employees}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="dep_hr"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Department HR</FormLabel>
                <FormControl>
                  <EmployeeSelect
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select Department HR"
                    employees={employees}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="department_status_active"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Active Status</FormLabel>
                  <FormDescription>
                    Mark this department as active
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="parent_department"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Parent Department</FormLabel>
                <FormControl>
                  <DepartmentSelect
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select parent department"
                    departments={departments}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex justify-end pt-4">
            <Button type="submit" disabled={isSubmitting} className="min-w-[120px]">
              {isSubmitting ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                submitButtonText
              )}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
