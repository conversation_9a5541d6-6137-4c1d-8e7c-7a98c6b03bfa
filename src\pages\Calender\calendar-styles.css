/* Custom styles for the calendar component */

.calendar-container .rbc-calendar {
  border-radius: 0.5rem;
  overflow: hidden;
}

.calendar-container .rbc-header {
  padding: 10px 0;
  font-weight: 600;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.calendar-container .rbc-month-view {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.calendar-container .rbc-day-bg {
  transition: background-color 0.2s;
}

.calendar-container .rbc-day-bg:hover {
  background-color: #f9fafb;
}

.calendar-container .rbc-off-range-bg {
  background-color: #f9fafb;
}

.calendar-container .rbc-today {
  background-color: rgba(59, 130, 246, 0.1);
}

.calendar-container .rbc-event {
  border-radius: 4px;
  padding: 2px 5px;
  font-size: 0.8rem;
  border: none;
  transition: transform 0.1s, box-shadow 0.2s;
}

.calendar-container .rbc-event:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

/* Custom styles for different event types */
.calendar-container .rbc-event[title*="Non-working"] {
  background-image: repeating-linear-gradient(
    45deg,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0) 5px,
    rgba(0, 0, 0, 0.1) 5px,
    rgba(0, 0, 0, 0.1) 10px
  );
}

.calendar-container .rbc-event[title*="Restricted"] {
  background-image: repeating-linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0) 5px,
    rgba(255, 255, 255, 0.1) 5px,
    rgba(255, 255, 255, 0.1) 10px
  );
}

.calendar-container .rbc-toolbar {
  margin-bottom: 1rem;
  padding: 0.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.calendar-container .rbc-toolbar button {
  color: #4b5563;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.calendar-container .rbc-toolbar button:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.calendar-container .rbc-toolbar button.rbc-active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.calendar-container .rbc-toolbar button.rbc-active:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .calendar-container .rbc-header {
    background-color: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
  }

  .calendar-container .rbc-month-view {
    border-color: #374151;
  }

  .calendar-container .rbc-off-range-bg {
    background-color: #111827;
  }

  .calendar-container .rbc-day-bg:hover {
    background-color: #1f2937;
  }

  .calendar-container .rbc-today {
    background-color: rgba(59, 130, 246, 0.2);
  }

  .calendar-container .rbc-toolbar button {
    color: #e5e7eb;
    background-color: #1f2937;
    border-color: #374151;
  }

  .calendar-container .rbc-toolbar button:hover {
    background-color: #374151;
    border-color: #4b5563;
  }
}
