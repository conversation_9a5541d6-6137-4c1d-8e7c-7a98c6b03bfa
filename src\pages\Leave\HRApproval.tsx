import React, { useEffect, useState, useMemo } from "react";
import dayjs from "dayjs";
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { BASE_URL } from "@/config";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON>rian<PERSON>, <PERSON><PERSON><PERSON><PERSON>cle, CheckCircle, Search } from "lucide-react";
import { useDebounce } from "@/hooks/use-debounce";

type HRLeaveApplicationApi = {
  id: number;
  employee_no: string;
  leave_type_name: string;
  start_date: string;
  end_date: string;
  no_of_days_applied: number;
  return_date: string;
  leave_status: string;
  acknowledger: string | null;
  first_acknowledger: string | null;
  second_acknowledger: string | null;
};

type HRLeaveApplication = {
  id: number;
  employeeNo: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  days: number;
  returnDate: string;
  status: string;
  acknowledger: string | null;
  firstAcknowledger: string | null;
  secondAcknowledger: string | null;
};

type EmployeeData = {
  employee_no: string;
  first_name: string;
  middle_name: string;
  last_name: string;
};

function mapApiToUi(leave: HRLeaveApplicationApi): HRLeaveApplication {
  return {
    id: leave.id,
    employeeNo: leave.employee_no,
    leaveType: leave.leave_type_name,
    startDate: leave.start_date,
    endDate: leave.end_date,
    days: leave.no_of_days_applied,
    returnDate: leave.return_date,
    status: leave.leave_status,
    acknowledger: leave.acknowledger,
    firstAcknowledger: leave.first_acknowledger,
    secondAcknowledger: leave.second_acknowledger,
  };
}

function formatDate(dateString: string) {
  return dayjs(dateString).format("DD-MM-YYYY");
}

function getStatusClass(status: string) {
  switch (status) {
    case "Approved by HR":
      return "bg-green-600 text-white";
    case "Rejected by HR":
    case "Rejected by Supervisor":
    case "Acknowledge Rejected":
      return "bg-red-600 text-white";
    case "Approved by Supervisor":
      return "bg-yellow-400 text-black";
    case "Acknowledged":
      return "bg-blue-500 text-white";
    case "Taken":
      return "bg-blue-600 text-white";
    default:
      return "bg-gray-500 text-white";
  }
}

export default function HRApprovalPage() {
  const { token } = useSelector((state: RootState) => state.auth);

  // Existing
  const [hrLeaveApplications, setHRLeaveApplications] = useState<
    HRLeaveApplication[]
  >([]);
  const [bypassLeaveApplications, setBypassLeaveApplications] = useState<
    HRLeaveApplication[]
  >([]);
  const [forgottenLeaveApplications, setForgottenLeaveApplications] = useState<
    HRLeaveApplication[]
  >([]);
  // New tabs
  const [supervisorApprovalLeaves, setSupervisorApprovalLeaves] = useState<HRLeaveApplication[]>(
    []
  );
  const [completedLeaves, setCompletedLeaves] = useState<HRLeaveApplication[]>(
    []
  );
  // Leave History tab
  const [allLeaves, setAllLeaves] = useState<HRLeaveApplication[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Employees
  const [employees, setEmployees] = useState<EmployeeData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<{
    message: string;
    type: "success" | "error";
  } | null>(null);
  // Pagination for all tabs
  const [currentPage, setCurrentPage] = useState(1);
  const [bypassCurrentPage, setBypassCurrentPage] = useState(1);
  const [forgottenCurrentPage, setForgottenCurrentPage] = useState(1);
  const [supervisorCurrentPage, setSupervisorCurrentPage] = useState(1);
  const [completedCurrentPage, setCompletedCurrentPage] = useState(1);
  const [historyCurrentPage, setHistoryCurrentPage] = useState(1);
  const pageSize = 10;

  // Filter leave history based on search term
  const filteredLeaveHistory = useMemo(() => {
    if (!debouncedSearchTerm.trim()) return allLeaves;

    const searchLower = debouncedSearchTerm.toLowerCase();
    return allLeaves.filter((leave) => {
      const employeeName = getEmployeeName(leave.employeeNo).toLowerCase();
      return employeeName.includes(searchLower);
    });
  }, [allLeaves, debouncedSearchTerm, employees]);

  // Paginated data
  const totalPages = Math.ceil(hrLeaveApplications.length / pageSize);
  const bypassTotalPages = Math.ceil(bypassLeaveApplications.length / pageSize);
  const forgottenTotalPages = Math.ceil(
    forgottenLeaveApplications.length / pageSize
  );
  const supervisorTotalPages = Math.ceil(supervisorApprovalLeaves.length / pageSize);
  const completedTotalPages = Math.ceil(completedLeaves.length / pageSize);
  const historyTotalPages = Math.ceil(filteredLeaveHistory.length / pageSize);

  const paginatedData = hrLeaveApplications.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );
  const bypassPaginatedData = bypassLeaveApplications.slice(
    (bypassCurrentPage - 1) * pageSize,
    bypassCurrentPage * pageSize
  );
  const forgottenPaginatedData = forgottenLeaveApplications.slice(
    (forgottenCurrentPage - 1) * pageSize,
    forgottenCurrentPage * pageSize
  );
  const supervisorPaginatedData = supervisorApprovalLeaves.slice(
    (supervisorCurrentPage - 1) * pageSize,
    supervisorCurrentPage * pageSize
  );
  const completedPaginatedData = completedLeaves.slice(
    (completedCurrentPage - 1) * pageSize,
    completedCurrentPage * pageSize
  );
  const historyPaginatedData = filteredLeaveHistory.slice(
    (historyCurrentPage - 1) * pageSize,
    historyCurrentPage * pageSize
  );

  const [selectedLeave, setSelectedLeave] = useState<HRLeaveApplication | null>(
    null
  );
  const [openModal, setOpenModal] = useState(false);
  const [activeTab, setActiveTab] = useState("regular");

  function getEmployeeName(empNo: string | null) {
    if (!empNo) return "";
    const emp = employees.find((e) => e.employee_no === empNo);
    if (!emp) return empNo;
    return `${emp.first_name} ${emp.middle_name} ${emp.last_name}`;
  }

  // Fetch all leave data and filter for all tab categories
  const fetchAllLeaveData = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch(`${BASE_URL}/hrm/leave-application-details`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error("Failed to fetch leaves");
      const data = (await res.json()) as HRLeaveApplicationApi[];

      const today = dayjs();

      // Sort all data by ID descending (latest first)
      const sortedData = data.sort((a, b) => b.id - a.id);

      // All Leaves for History tab (sorted by latest first)
      setAllLeaves(sortedData.map(mapApiToUi));

      // Regular Approval: Approved by Supervisor (sorted by latest first)
      setHRLeaveApplications(
        sortedData
          .filter((leave) => leave.leave_status === "Approved by Supervisor")
          .map(mapApiToUi)
      );

      // Bypass: Acknowledged and 1 day or less to start date, not already handled (sorted by latest first)
      setBypassLeaveApplications(
        sortedData
          .filter((leave) => {
            const startDate = dayjs(leave.start_date);
            const daysUntilLeave = startDate.diff(today, "day");
            return (
              (leave.leave_status === "Acknowledged" ||
                leave.acknowledger !== null) &&
              daysUntilLeave <= 1 &&
              daysUntilLeave >= 0 &&
              ![
                "Taken",
                "Pending",
                "Approved by Supervisor",
                "Rejected by Supervisor",
                "Acknowledge Rejected",
                "Approved by HR",
                "Rejected by HR",
              ].includes(leave.leave_status)
            );
          })
          .map(mapApiToUi)
      );

      // Forgotten: Past start date, not handled yet (sorted by latest first)
      setForgottenLeaveApplications(
        sortedData
          .filter((leave) => {
            const startDate = dayjs(leave.start_date);
            const daysFromStart = today.diff(startDate, "day");
            return (
              daysFromStart > 0 &&
              ![
                "Taken",
                "Acknowledge Rejected,",
                "Rejected by Supervisor",
                "Approved by HR",
                "Rejected by HR",
              ].includes(leave.leave_status)
            );
          })
          .map(mapApiToUi)
      );

      // Supervisor Approval: Acknowledged leaves (sorted by latest first)
      setSupervisorApprovalLeaves(
        sortedData
          .filter((leave) => leave.leave_status === "Acknowledged")
          .map(mapApiToUi)
      );

      // Completed Leaves (sorted by latest first)
      setCompletedLeaves(
        sortedData.filter((leave) => leave.leave_status === "Approved by HR").map(mapApiToUi)
      );
    } catch (err: any) {
      setError(err.message || "Error fetching leaves");
    } finally {
      setLoading(false);
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const memoizedFetchAllLeaveData = React.useCallback(fetchAllLeaveData, [
    token,
  ]);

  useEffect(() => {
    async function fetchEmployees() {
      try {
        const res = await fetch(`${BASE_URL}/users/employee-bio-details`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) throw new Error("Failed to fetch employees");
        const data = (await res.json()) as EmployeeData[];
        setEmployees(data);
      } catch (error) {
        console.error("Error fetching employees:", error);
      }
    }
    if (token) {
      memoizedFetchAllLeaveData();
      fetchEmployees();
    }
  }, [token, memoizedFetchAllLeaveData]);

  // Reset history page when search term changes
  useEffect(() => {
    setHistoryCurrentPage(1);
  }, [debouncedSearchTerm]);

  // Approve/Reject handlers (reuse for all tabs)
  const handleApprove = async (id: number) => {
    try {
      const patchData = { leave_status: "Approved by HR" };
      const res = await fetch(
        `${BASE_URL}/hrm/leave-application-details/${id}`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Token ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(patchData),
        }
      );
      if (!res.ok) throw new Error("Failed to approve leave");
      await fetchAllLeaveData();
      setNotification({
        message: "Leave approved successfully!",
        type: "success",
      });
      setOpenModal(false);
    } catch (err: any) {
      setNotification({
        message: err.message || "Error approving leave",
        type: "error",
      });
    }
  };
  const handleReject = async (id: number) => {
    try {
      const patchData = { leave_status: "Rejected by HR" };
      const res = await fetch(
        `${BASE_URL}/hrm/leave-application-details/${id}`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Token ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(patchData),
        }
      );
      if (!res.ok) throw new Error("Failed to reject leave");
      await fetchAllLeaveData();
      setNotification({
        message: "Leave rejected successfully.",
        type: "error",
      });
      setOpenModal(false);
    } catch (err: any) {
      setNotification({
        message: err.message || "Error rejecting leave",
        type: "error",
      });
    }
  };

  // Pagination handlers for all tabs
  const handleNext = () => {
    if (currentPage < totalPages) setCurrentPage((p) => p + 1);
  };
  const handlePrev = () => {
    if (currentPage > 1) setCurrentPage((p) => p - 1);
  };
  const handleBypassNext = () => {
    if (bypassCurrentPage < bypassTotalPages)
      setBypassCurrentPage((p) => p + 1);
  };
  const handleBypassPrev = () => {
    if (bypassCurrentPage > 1) setBypassCurrentPage((p) => p - 1);
  };
  const handleForgottenNext = () => {
    if (forgottenCurrentPage < forgottenTotalPages)
      setForgottenCurrentPage((p) => p + 1);
  };
  const handleForgottenPrev = () => {
    if (forgottenCurrentPage > 1) setForgottenCurrentPage((p) => p - 1);
  };
  const handleSupervisorNext = () => {
    if (supervisorCurrentPage < supervisorTotalPages)
      setSupervisorCurrentPage((p) => p + 1);
  };
  const handleSupervisorPrev = () => {
    if (supervisorCurrentPage > 1) setSupervisorCurrentPage((p) => p - 1);
  };
  const handleCompletedNext = () => {
    if (completedCurrentPage < completedTotalPages)
      setCompletedCurrentPage((p) => p + 1);
  };
  const handleCompletedPrev = () => {
    if (completedCurrentPage > 1) setCompletedCurrentPage((p) => p - 1);
  };
  const handleHistoryNext = () => {
    if (historyCurrentPage < historyTotalPages)
      setHistoryCurrentPage((p) => p + 1);
  };
  const handleHistoryPrev = () => {
    if (historyCurrentPage > 1) setHistoryCurrentPage((p) => p - 1);
  };

  const dismissNotification = () => setNotification(null);

  // Table rendering utility for DRYness
  const renderTable = (data: HRLeaveApplication[], actions?: boolean) => (
    <div className="overflow-x-auto">
      <Table className="min-w-full">
        <TableHeader>
          <TableRow>
            <TableHead>Employee Name</TableHead>
            <TableHead>Leave Type</TableHead>
            <TableHead>Start Date</TableHead>
            <TableHead>End Date</TableHead>
            <TableHead>Days</TableHead>
            <TableHead>Return Date</TableHead>
            <TableHead>Status</TableHead>
            {actions && <TableHead>Action</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={actions ? 8 : 7} className="text-center">
                No records found.
              </TableCell>
            </TableRow>
          ) : (
            data.map((leave) => (
              <TableRow key={leave.id}>
                <TableCell className="font-medium">
                  {getEmployeeName(leave.employeeNo)}
                </TableCell>
                <TableCell>{leave.leaveType}</TableCell>
                <TableCell>
                  {leave.status === "Taken" ? (
                    <span className="text-blue-600 font-medium">
                      {formatDate(leave.startDate)}
                    </span>
                  ) : leave.status.includes("Rejected") ? (
                    <span className="text-red-600 font-medium">
                      {formatDate(leave.startDate)}
                    </span>
                  ) : (
                    formatDate(leave.startDate)
                  )}
                </TableCell>
                <TableCell>{formatDate(leave.endDate)}</TableCell>
                <TableCell>{leave.days}</TableCell>
                <TableCell>{formatDate(leave.returnDate)}</TableCell>
                <TableCell>
                  <Badge
                    className={`inline-flex items-center text-xs px-2 py-0.5 leading-none rounded-full ${getStatusClass(
                      leave.status
                    )}`}
                  >
                    {leave.status}
                  </Badge>
                </TableCell>
                {actions && (
                  <TableCell>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => {
                        setSelectedLeave(leave);
                        setOpenModal(true);
                      }}
                    >
                      View
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <Screen headerContent={<h1 className="text-lg font-bold">HR Approval</h1>}>
      <div className="container mx-auto p-6">
        {loading && <p className="text-sm mb-2">Loading leaves...</p>}
        {error && <p className="text-sm text-red-500 mb-2">{error}</p>}
        {notification && (
          <div className="fixed top-5 right-5 z-50">
            <Alert
              className="flex items-center space-x-2"
              onClick={dismissNotification}
            >
              {notification.type === "success" ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <div className="flex-1">
                <AlertTitle>
                  {notification.type === "success" ? "Success" : "Error"}
                </AlertTitle>
                <AlertDescription>{notification.message}</AlertDescription>
              </div>
            </Alert>
          </div>
        )}

        <Tabs
          defaultValue="regular"
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 mb-4 h-auto">
            <TabsTrigger value="regular" className="text-xs sm:text-sm">
              HR Approval
            </TabsTrigger>
            <TabsTrigger value="supervisor" className="text-xs sm:text-sm">
              Supervisor Approval
              {supervisorApprovalLeaves.length > 0 && (
                <Badge className="ml-1 sm:ml-2 bg-blue-500 text-white text-xs">
                  {supervisorApprovalLeaves.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="bypass" className="text-xs sm:text-sm">
              Bypass Approval
              {bypassLeaveApplications.length > 0 && (
                <Badge className="ml-1 sm:ml-2 bg-red-500 text-white text-xs">
                  {bypassLeaveApplications.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="forgotten" className="text-xs sm:text-sm">
              Forgotten Leaves
              {forgottenLeaveApplications.length > 0 && (
                <Badge className="ml-1 sm:ml-2 bg-orange-500 text-white text-xs">
                  {forgottenLeaveApplications.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="completed" className="text-xs sm:text-sm">
              Completed Leaves
              {completedLeaves.length > 0 && (
                <Badge className="ml-1 sm:ml-2 bg-blue-600 text-white text-xs">
                  {completedLeaves.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="history" className="text-xs sm:text-sm">
              Leave History
              {allLeaves.length > 0 && (
                <Badge className="ml-1 sm:ml-2 bg-green-600 text-white text-xs">
                  {allLeaves.length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          {/* Regular Approval Tab */}
          <TabsContent value="regular">
            <Card className="pt-8">
              <CardContent>
                {renderTable(paginatedData, true)}
                <div className="mt-4 flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={handlePrev}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {currentPage} of {totalPages || 1}
                  </span>
                  <Button
                    variant="outline"
                    onClick={handleNext}
                    disabled={currentPage === totalPages || totalPages === 0}
                  >
                    Next
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Bypass Approval Tab */}
          <TabsContent value="bypass">
            <Card className="pt-8">
              <CardContent>
                <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-md">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-amber-600 mr-2 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium text-amber-800">
                        Supervisor Bypass Required
                      </h3>
                      <p className="text-sm text-amber-700 mt-1">
                        These leave applications are due to start within one day
                        but have not been approved by a supervisor. As HR, you
                        can bypass the supervisor approval step to either
                        approve or reject these leaves directly.
                      </p>
                    </div>
                  </div>
                </div>
                {renderTable(bypassPaginatedData, true)}
                <div className="mt-4 flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={handleBypassPrev}
                    disabled={bypassCurrentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {bypassCurrentPage} of {bypassTotalPages || 1}
                  </span>
                  <Button
                    variant="outline"
                    onClick={handleBypassNext}
                    disabled={
                      bypassCurrentPage === bypassTotalPages ||
                      bypassTotalPages === 0
                    }
                  >
                    Next
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Forgotten Tab */}
          <TabsContent value="forgotten">
            <Card className="pt-8">
              <CardContent>
                <div className="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-md">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-orange-600 mr-2 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium text-orange-800">
                        Forgotten Leave Applications
                      </h3>
                      <p className="text-sm text-orange-700 mt-1">
                        These leave applications have start dates that have
                        already passed but have not been approved or rejected.
                        Please review and take appropriate action.
                      </p>
                    </div>
                  </div>
                </div>
                {renderTable(forgottenPaginatedData, true)}
                <div className="mt-4 flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={handleForgottenPrev}
                    disabled={forgottenCurrentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {forgottenCurrentPage} of {forgottenTotalPages || 1}
                  </span>
                  <Button
                    variant="outline"
                    onClick={handleForgottenNext}
                    disabled={
                      forgottenCurrentPage === forgottenTotalPages ||
                      forgottenTotalPages === 0
                    }
                  >
                    Next
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Supervisor Approval Tab */}
          <TabsContent value="supervisor">
            <Card className="pt-8">
              <CardContent>
                <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium text-blue-800">
                        Supervisor Approval Required
                      </h3>
                      <p className="text-sm text-blue-700 mt-1">
                        These leave applications have been acknowledged by employees
                        and are awaiting supervisor approval. As HR, you can review
                        and take appropriate action on these applications.
                      </p>
                    </div>
                  </div>
                </div>
                {renderTable(supervisorPaginatedData, false)}
                <div className="mt-4 flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={handleSupervisorPrev}
                    disabled={supervisorCurrentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {supervisorCurrentPage} of {supervisorTotalPages || 1}
                  </span>
                  <Button
                    variant="outline"
                    onClick={handleSupervisorNext}
                    disabled={
                      supervisorCurrentPage === supervisorTotalPages ||
                      supervisorTotalPages === 0
                    }
                  >
                    Next
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Completed Leaves Tab */}
          <TabsContent value="completed">
            <Card className="pt-8">
              <CardContent>
                <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium text-green-800">
                        Completed Leave Applications
                      </h3>
                      <p className="text-sm text-green-700 mt-1">
                        These leave applications have been taken by the
                        employees. You may view them here
                      </p>
                    </div>
                  </div>
                </div>
                {renderTable(completedPaginatedData, false)}
                <div className="mt-4 flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={handleCompletedPrev}
                    disabled={completedCurrentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {completedCurrentPage} of {completedTotalPages || 1}
                  </span>
                  <Button
                    variant="outline"
                    onClick={handleCompletedNext}
                    disabled={
                      completedCurrentPage === completedTotalPages ||
                      completedTotalPages === 0
                    }
                  >
                    Next
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Leave History Tab */}
          <TabsContent value="history">
            <Card className="pt-8">
              <CardContent>
                <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium text-blue-800">
                        Complete Leave History
                      </h3>
                      <p className="text-sm text-blue-700 mt-1">
                        View all leave applications across all statuses. Search by employee name to view individual leave history.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Search Bar */}
                <div className="mb-4">
                  <div className="relative max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by employee name..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 h-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  {debouncedSearchTerm && (
                    <p className="text-sm text-gray-600 mt-2">
                      Showing results for "{debouncedSearchTerm}" ({filteredLeaveHistory.length} found)
                    </p>
                  )}
                </div>

                {renderTable(historyPaginatedData, false)}
                <div className="mt-4 flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={handleHistoryPrev}
                    disabled={historyCurrentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {historyCurrentPage} of {historyTotalPages || 1}
                  </span>
                  <Button
                    variant="outline"
                    onClick={handleHistoryNext}
                    disabled={
                      historyCurrentPage === historyTotalPages ||
                      historyTotalPages === 0
                    }
                  >
                    Next
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Modal for Approve/Reject */}
      <Dialog open={openModal} onOpenChange={setOpenModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Leave Details</DialogTitle>
          </DialogHeader>
          {selectedLeave && (
            <div className="space-y-2">
              <p>
                <strong>Employee Name:</strong>{" "}
                {getEmployeeName(selectedLeave.employeeNo)}
              </p>
              <p>
                <strong>Acknowledger Name:</strong>{" "}
                {getEmployeeName(selectedLeave.acknowledger)}
              </p>
              <p>
                <strong>Supervisor Name:</strong>{" "}
                {getEmployeeName(selectedLeave.secondAcknowledger)}
              </p>
              <p>
                <strong>Leave Type:</strong> {selectedLeave.leaveType}
              </p>
              <p>
                <strong>Start Date:</strong>{" "}
                {formatDate(selectedLeave.startDate)}
              </p>
              <p>
                <strong>End Date:</strong> {formatDate(selectedLeave.endDate)}
              </p>
              <p>
                <strong>Days:</strong> {selectedLeave.days}
              </p>
              <p>
                <strong>Return Date:</strong>{" "}
                {formatDate(selectedLeave.returnDate)}
              </p>
              <p>
                <strong>Status:</strong> {selectedLeave.status}
              </p>
            </div>
          )}
          <DialogFooter>
            {selectedLeave && (
              <div className="flex w-full justify-end space-x-2">
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handleApprove(selectedLeave.id)}
                >
                  Approve
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleReject(selectedLeave.id)}
                >
                  Reject
                </Button>
              </div>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Screen>
  );
}
